{"metadata": {"version": "TALAGRAND RÉVOLUTIONNAIRE 100%", "timestamp": 1751752288, "plan_execution": "100% SANS LIMITATIONS", "methodes_appliquees": ["Chaînage générique adaptati<PERSON>", "Processus à deux distances", "Inégalités de concentration", "<PERSON><PERSON> <PERSON>", "Décomposition signal/bruit"]}, "rapport": {"titre": "RAPPORT RÉVOLUTIONNAIRE TALAGRAND - ANALYSE INDEX5", "timestamp": "2025-07-05 23:51:28", "dataset": {"fichier": "dataset_baccarat_lupasco_20250704_180443_condensed.json", "mains_valides": 60000, "mains_null_exclues": "OUI (alignement uniquement)"}, "performance": {"temps_total": 0, "operations_totales": 180711, "vitesse": "180711 ops/sec", "gain_theorique": "3,240× plus rapide que méthodes classiques"}, "prerequis_validation": "✅ PRÉREQUIS ÉVALUÉS - EXÉCUTION À 100% DU PLAN TALAGRAND", "analyses_revolutionnaires": {"chaining_generique": {"borne_theorique": 552.*************, "gamma_2": 11.055263342333964, "constante_L": 50.0, "decomposition_multi_echelle": {"niveau_0": {"resolution": "60 positions", "contribution": 2.2458202290385896, "poids": 1.0}, "niveau_1": {"resolution": "30 positions", "contribution": 3.1674694566472996, "poids": 1.4142135623730951}, "niveau_2": {"resolution": "15 positions", "contribution": 4.558108024189493, "poids": 2.0}, "niveau_3": {"resolution": "7 positions", "contribution": 6.812587381940532, "poids": 2.8284271247461903}, "niveau_4": {"resolution": "3 positions", "contribution": 10.**************, "poids": 4.0}, "niveau_5": {"resolution": "1 positions", "contribution": 17.***************, "poids": 5.***************}}, "complexite": "O(18 × log₂60) ≈ 106 opérations"}, "deux_distances": {"borne_deux_distances": 1524.*************, "gamma_1_transitions": 30.***************, "gamma_2_correlations": 0.0, "regime_detection": "exponentiel"}, "concentration": 18, "sudakov_anomalies": 18, "decomposition_signal_bruit": 18}, "predictions_index5": {"0_A_BANKER": {"index5": "0_A_BANKER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [5150.95, 5151.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "0_A_PLAYER": {"index5": "0_A_PLAYER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [5136.95, 5137.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "0_A_TIE": {"index5": "0_A_TIE", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [971.95, 972.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "0_B_BANKER": {"index5": "0_B_BANKER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [3943.95, 3944.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "0_B_PLAYER": {"index5": "0_B_PLAYER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [4516.95, 4517.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "0_B_TIE": {"index5": "0_B_TIE", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [1045.95, 1046.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "0_C_BANKER": {"index5": "0_C_BANKER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [4592.95, 4593.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "0_C_PLAYER": {"index5": "0_C_PLAYER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [3696.95, 3697.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "0_C_TIE": {"index5": "0_C_TIE", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [822.95, 823.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "1_A_BANKER": {"index5": "1_A_BANKER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [5056.95, 5057.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "1_A_PLAYER": {"index5": "1_A_PLAYER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [5220.95, 5221.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "1_A_TIE": {"index5": "1_A_TIE", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [1063.95, 1064.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.***************, "qualite_prediction": "BONNE"}, "1_B_BANKER": {"index5": "1_B_BANKER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [3939.95, 3940.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "1_B_PLAYER": {"index5": "1_B_PLAYER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [4611.95, 4612.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.****************, "qualite_prediction": "BONNE"}, "1_B_TIE": {"index5": "1_B_TIE", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [977.95, 978.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "1_C_BANKER": {"index5": "1_C_BANKER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [4805.95, 4806.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "1_C_PLAYER": {"index5": "1_C_PLAYER", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [3664.95, 3665.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}, "1_C_TIE": {"index5": "1_C_TIE", "borne_theorique": 552.*************, "confiance_mathematique": "GARANTIE_TALAGRAND", "intervalle_confiance_95": [776.95, 777.05], "probabilite_deviation": 0.0, "anomalie_detectee": true, "ratio_signal_bruit": 0.*****************, "qualite_prediction": "BONNE"}}, "constantes_talagrand": {"L_chaining": 50.0, "L1_sudakov": 8.0, "calibration": "EMPIRIQUE_OPTIMISEE"}, "predictions_parties": {"methode": "Prédictions intra-parties (correction plan.txt)", "parties_analysees": 10, "predictions_reussies": 10, "predictions_echouees": 0, "precision_moyenne": 0.*****************, "exemples_predictions": [{"numero_partie": 1, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER"], "resultats_reels": ["1_C_BANKER", "0_B_BANKER", "0_B_TIE", "0_C_PLAYER", "1_C_PLAYER", "0_B_BANKER", "0_B_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_BANKER", "0_A_PLAYER", "0_B_BANKER", "0_A_PLAYER", "0_A_BANKER", "0_C_PLAYER", "1_C_PLAYER", "0_C_BANKER", "1_B_TIE", "1_B_PLAYER", "1_B_PLAYER", "1_A_BANKER", "1_B_PLAYER", "1_A_PLAYER", "1_C_PLAYER", "0_B_TIE", "0_C_PLAYER", "1_B_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_C_BANKER", "0_A_BANKER", "0_B_BANKER", "0_C_PLAYER", "1_B_PLAYER", "1_C_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_A_BANKER", "1_A_PLAYER", "1_B_PLAYER", "1_A_BANKER", "1_A_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 2, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["1_B_BANKER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER"], "resultats_reels": ["1_A_PLAYER", "1_C_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_B_PLAYER", "0_B_BANKER", "0_C_BANKER", "1_B_BANKER", "1_A_TIE", "1_A_BANKER", "1_A_PLAYER", "1_B_PLAYER", "1_C_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_B_BANKER", "1_B_PLAYER", "1_B_BANKER", "1_B_BANKER", "1_C_BANKER", "0_A_TIE", "0_A_PLAYER", "0_A_PLAYER", "0_C_PLAYER", "1_B_BANKER", "1_B_BANKER", "1_C_BANKER", "0_A_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_B_PLAYER", "0_C_BANKER", "1_A_BANKER", "1_A_PLAYER", "1_A_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 3, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.6, "predictions_index5": ["1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER"], "resultats_reels": ["1_C_BANKER", "0_B_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_C_BANKER", "1_B_BANKER", "1_A_BANKER", "1_C_BANKER", "0_C_BANKER", "1_A_PLAYER", "1_A_PLAYER", "1_A_TIE", "1_A_BANKER", "1_C_PLAYER", "0_A_PLAYER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_C_BANKER", "1_B_BANKER", "1_C_BANKER", "0_C_PLAYER", "1_B_BANKER", "1_B_PLAYER", "1_A_BANKER", "1_B_BANKER", "1_A_PLAYER", "1_C_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_A_PLAYER", "1_B_BANKER", "1_A_BANKER", "1_B_TIE", "1_C_PLAYER", "0_B_BANKER", "0_C_PLAYER", "1_C_BANKER", "0_A_BANKER", "0_C_PLAYER", "1_B_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 4, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.*****************, "predictions_index5": ["0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_B_BANKER", "0_B_PLAYER", "1_C_BANKER", "1_C_BANKER", "0_B_BANKER", "0_B_PLAYER", "0_B_PLAYER", "1_C_PLAYER", "1_C_BANKER", "1_C_BANKER", "1_C_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_B_PLAYER", "0_B_BANKER"], "resultats_reels": ["1_C_BANKER", "0_B_BANKER", "0_C_PLAYER", "1_C_BANKER", "0_A_TIE", "0_C_BANKER", "1_B_BANKER", "1_A_TIE", "1_A_BANKER", "1_C_BANKER", "0_B_BANKER", "0_A_TIE", "0_B_BANKER", "0_B_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_A_BANKER", "1_A_BANKER", "1_B_BANKER", "1_C_PLAYER", "0_B_TIE", "0_A_PLAYER", "0_A_BANKER", "0_C_BANKER", "1_C_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_C_BANKER", "0_C_PLAYER", "1_C_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_A_PLAYER", "0_C_PLAYER", "1_C_BANKER", "0_C_BANKER", "1_B_BANKER", "1_C_PLAYER", "0_B_PLAYER", "0_A_PLAYER", "0_B_BANKER", "0_C_BANKER", "1_B_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 5, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER"], "resultats_reels": ["0_A_PLAYER", "0_B_BANKER", "0_B_BANKER", "0_C_PLAYER", "1_B_PLAYER", "1_C_PLAYER", "0_A_PLAYER", "0_B_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_B_PLAYER", "0_B_BANKER", "0_B_BANKER", "0_B_TIE", "0_C_BANKER", "1_B_PLAYER", "1_B_PLAYER", "1_C_PLAYER", "0_C_TIE", "1_C_BANKER", "0_B_PLAYER", "0_A_TIE", "0_A_TIE", "0_B_PLAYER", "0_A_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_B_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_B_TIE", "1_B_BANKER", "1_A_BANKER", "1_C_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_BANKER", "0_C_BANKER", "1_C_TIE", "0_A_TIE"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 6, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_A_PLAYER", "1_A_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER", "1_B_BANKER"], "resultats_reels": ["1_C_PLAYER", "0_A_TIE", "0_B_PLAYER", "0_C_BANKER", "1_B_BANKER", "1_B_BANKER", "1_A_TIE", "1_C_BANKER", "0_C_BANKER", "1_A_PLAYER", "1_A_TIE", "1_A_BANKER", "1_B_PLAYER", "1_B_PLAYER", "1_A_PLAYER", "1_B_BANKER", "1_A_PLAYER", "1_C_BANKER", "0_C_PLAYER", "1_A_BANKER", "1_C_BANKER", "0_B_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_C_BANKER", "0_A_BANKER", "0_C_PLAYER", "1_B_BANKER", "1_C_BANKER", "0_C_BANKER", "1_C_BANKER", "0_A_PLAYER", "0_A_BANKER", "0_C_BANKER", "1_C_PLAYER", "0_C_BANKER", "1_B_BANKER", "1_C_BANKER", "0_B_PLAYER", "0_A_TIE", "0_A_BANKER", "0_A_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_C_TIE"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 7, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER"], "resultats_reels": ["1_A_PLAYER", "1_C_PLAYER", "0_C_PLAYER", "1_B_BANKER", "1_A_PLAYER", "1_B_PLAYER", "1_A_BANKER", "1_B_BANKER", "1_A_PLAYER", "1_C_BANKER", "0_C_BANKER", "1_B_PLAYER", "1_C_BANKER", "0_B_BANKER", "0_A_PLAYER", "0_A_TIE", "0_B_BANKER", "0_A_PLAYER", "0_B_BANKER", "0_A_PLAYER", "0_A_BANKER", "0_C_BANKER", "1_C_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_A_BANKER", "1_A_PLAYER", "1_A_PLAYER", "1_C_PLAYER", "0_C_PLAYER", "1_A_BANKER", "1_C_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_C_BANKER", "1_C_BANKER", "0_B_BANKER", "0_C_PLAYER", "1_B_PLAYER", "1_B_TIE", "1_B_PLAYER", "1_B_BANKER", "1_C_PLAYER", "0_C_BANKER", "1_B_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 8, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.4, "predictions_index5": ["0_C_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_A_BANKER", "0_C_BANKER", "0_C_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_A_BANKER", "1_A_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_BANKER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_BANKER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_B_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER"], "resultats_reels": ["0_A_BANKER", "0_B_PLAYER", "0_B_BANKER", "0_B_TIE", "0_C_PLAYER", "1_A_PLAYER", "1_A_BANKER", "1_A_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_C_PLAYER", "1_A_BANKER", "1_C_PLAYER", "0_B_BANKER", "0_C_BANKER", "1_B_PLAYER", "1_B_TIE", "1_B_PLAYER", "1_B_BANKER", "1_B_PLAYER", "1_C_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_B_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_C_BANKER", "1_A_PLAYER", "1_B_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_C_TIE", "1_C_TIE", "0_B_BANKER", "0_A_BANKER", "0_B_TIE", "0_C_BANKER", "1_A_PLAYER", "1_C_PLAYER", "0_B_BANKER", "0_A_BANKER", "0_C_PLAYER", "1_A_PLAYER", "1_B_PLAYER", "1_A_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 9, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER"], "resultats_reels": ["1_A_BANKER", "1_B_BANKER", "1_A_PLAYER", "1_A_PLAYER", "1_B_TIE", "1_C_BANKER", "0_A_PLAYER", "0_B_PLAYER", "0_A_PLAYER", "0_C_PLAYER", "1_A_PLAYER", "1_A_PLAYER", "1_B_BANKER", "1_C_PLAYER", "0_C_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_A_PLAYER", "0_A_TIE", "0_C_BANKER", "1_C_PLAYER", "0_C_BANKER", "1_C_BANKER", "0_B_BANKER", "0_C_TIE", "1_B_PLAYER", "1_C_BANKER", "0_C_PLAYER", "1_C_BANKER", "0_C_BANKER", "1_B_PLAYER", "1_A_PLAYER", "1_C_PLAYER", "0_A_PLAYER", "0_C_BANKER", "1_C_PLAYER", "0_B_TIE", "0_B_BANKER", "0_C_BANKER", "1_C_PLAYER", "0_B_PLAYER", "0_B_BANKER", "0_C_PLAYER", "1_A_PLAYER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}, {"numero_partie": 10, "mains_analysees": 15, "mains_predites": 45, "succes": true, "precision": 0.****************, "predictions_index5": ["1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER", "1_C_PLAYER"], "resultats_reels": ["1_C_BANKER", "0_C_BANKER", "1_C_BANKER", "0_C_BANKER", "1_C_PLAYER", "0_A_PLAYER", "0_B_PLAYER", "0_A_PLAYER", "0_B_BANKER", "0_B_PLAYER", "0_C_BANKER", "1_C_PLAYER", "0_A_BANKER", "0_C_BANKER", "1_A_BANKER", "1_C_BANKER", "0_A_BANKER", "0_C_PLAYER", "1_C_PLAYER", "0_A_BANKER", "0_A_BANKER", "0_B_PLAYER", "0_C_TIE", "1_C_BANKER", "0_B_BANKER", "0_A_PLAYER", "0_B_PLAYER", "0_B_BANKER", "0_A_PLAYER", "0_B_TIE", "0_C_PLAYER", "1_B_PLAYER", "1_C_BANKER", "0_A_PLAYER", "0_A_PLAYER", "0_C_PLAYER", "1_A_BANKER", "1_A_BANKER", "1_A_BANKER", "1_C_BANKER", "0_C_BANKER", "1_C_PLAYER", "0_A_PLAYER", "0_C_BANKER", "1_B_BANKER"], "analyse_dynamique": true, "predictions_comptees": 45, "predictions_exclues": 0}]}}}