#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 PRÉDICTEUR ULTRA SIMPLE BACCARAT
Basé sur le tableau de transitions INDEX5 du rapport
"""

import json
import re
from typing import Dict, List, Optional

class PredicteurUltraSimple:
    """
    Prédicteur ultra simple basé sur le tableau de transitions INDEX5
    """
    
    def __init__(self):
        self.table_transitions = {}
        self.charger_table_transitions()
    
    def charger_table_transitions(self):
        """
        Charge la table de transitions depuis le fichier rapport
        """
        print("📊 Chargement de la table de transitions...")

        try:
            with open('rapport_transitions_index5_condense_20250705_201058.txt', 'r', encoding='utf-8') as f:
                contenu = f.read()

            # Extraire le premier tableau (lignes 9-26)
            lignes = contenu.split('\n')

            # Trouver les lignes de données directement
            # Les données commencent après la ligne avec INDEX5_SOURCE
            debut_donnees = None

            for i, ligne in enumerate(lignes):
                if 'INDEX5_SOURCE' in ligne and '|' in ligne:
                    debut_donnees = i + 2  # Après la ligne de séparation
                    break

            if debut_donnees is None:
                raise ValueError("Impossible de trouver l'en-tête INDEX5_SOURCE")

            # Parser les lignes de données (18 INDEX5 attendus)
            index5_attendus = [
                '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', '1_A_BANKER', '1_B_BANKER', '1_C_BANKER',
                '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER', '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER',
                '0_A_TIE', '0_B_TIE', '0_C_TIE', '1_A_TIE', '1_B_TIE', '1_C_TIE'
            ]

            for i in range(debut_donnees, min(debut_donnees + 20, len(lignes))):
                ligne = lignes[i].strip()
                if ligne and not ligne.startswith('-') and '|' in ligne:
                    # Vérifier si la ligne contient un INDEX5 attendu
                    for index5 in index5_attendus:
                        if ligne.startswith(index5):
                            self.parser_ligne_transition(ligne)
                            break

            print(f"✅ Table de transitions chargée : {len(self.table_transitions)} INDEX5")

            # Debug: afficher les INDEX5 chargés
            if len(self.table_transitions) == 0:
                print("🔍 Debug: Aucun INDEX5 trouvé. Affichage des premières lignes:")
                for i in range(min(30, len(lignes))):
                    if '|' in lignes[i]:
                        print(f"  Ligne {i}: {lignes[i][:100]}...")

        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            raise
    
    def parser_ligne_transition(self, ligne: str):
        """
        Parse une ligne du tableau de transitions
        Format: INDEX5_SOURCE | Main_02 | Main_03 | ... | Main_60
        """
        # Diviser par le séparateur |
        parties = [p.strip() for p in ligne.split('|')]
        
        if len(parties) < 60:  # INDEX5_SOURCE + 59 colonnes (Main_02 à Main_60)
            return
        
        index5_source = parties[0]
        
        # Créer le dictionnaire des prédictions pour cet INDEX5
        predictions = {}
        for i, prediction in enumerate(parties[1:], start=2):  # Commence à Main_02
            if prediction and prediction != '-':
                predictions[f"Main_{i:02d}"] = prediction
        
        self.table_transitions[index5_source] = predictions
    
    def charger_dataset(self, fichier_dataset: str) -> Dict:
        """
        Charge le dataset baccarat
        """
        print(f"📂 Chargement du dataset : {fichier_dataset}")
        
        try:
            with open(fichier_dataset, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            
            nb_parties = len(dataset.get('parties_condensees', []))
            print(f"✅ Dataset chargé : {nb_parties} parties")
            return dataset
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement du dataset : {e}")
            raise
    
    def extraire_index3_de_index5(self, index5: str) -> Optional[str]:
        """
        Extrait INDEX3 (BANKER/PLAYER/TIE) de INDEX5
        Format INDEX5: INDEX1_INDEX2_INDEX3
        """
        if not index5 or '_' not in index5:
            return None
        
        parties = index5.split('_')
        if len(parties) >= 3:
            return parties[2]  # INDEX3
        
        return None
    
    def predire_main_suivante(self, index5_actuel: str, position_actuelle: int) -> Optional[str]:
        """
        Prédit INDEX3 pour la main suivante basé sur INDEX5 actuel et position
        
        Args:
            index5_actuel: INDEX5 observé à la position actuelle
            position_actuelle: Position actuelle (1-59)
        
        Returns:
            INDEX3 prédit pour la position suivante (BANKER/PLAYER/TIE)
        """
        if not index5_actuel or index5_actuel not in self.table_transitions:
            return None
        
        position_suivante = position_actuelle + 1
        if position_suivante > 60:
            return None
        
        colonne_suivante = f"Main_{position_suivante:02d}"
        
        # Récupérer la prédiction INDEX5 pour la position suivante
        index5_predit = self.table_transitions[index5_actuel].get(colonne_suivante)
        
        if not index5_predit:
            return None
        
        # Extraire INDEX3 de l'INDEX5 prédit
        return self.extraire_index3_de_index5(index5_predit)
    
    def tester_predictions_partie(self, partie: Dict) -> Dict:
        """
        Teste les prédictions sur une partie complète
        
        Returns:
            Dictionnaire avec les résultats des prédictions
        """
        mains = partie.get('mains_condensees', [])
        partie_number = partie.get('partie_number', 0)
        
        resultats = {
            'partie_number': partie_number,
            'predictions': [],
            'total_predictions': 0,
            'predictions_correctes': 0,
            'precision': 0.0
        }
        
        # Parcourir les mains de 1 à 59 pour faire des prédictions
        for i in range(len(mains) - 1):
            main_actuelle = mains[i]
            main_suivante = mains[i + 1]
            
            # Vérifier que les mains sont valides
            if (main_actuelle.get('main_number') is None or 
                main_suivante.get('main_number') is None):
                continue
            
            position_actuelle = main_actuelle.get('main_number')
            index5_actuel = main_actuelle.get('index5')
            index3_reel_suivant = main_suivante.get('index3')
            
            if not index5_actuel or not index3_reel_suivant:
                continue
            
            # Faire la prédiction
            index3_predit = self.predire_main_suivante(index5_actuel, position_actuelle)
            
            if index3_predit is not None:
                est_correct = (index3_predit == index3_reel_suivant)
                
                resultats['predictions'].append({
                    'position_actuelle': position_actuelle,
                    'index5_actuel': index5_actuel,
                    'index3_predit': index3_predit,
                    'index3_reel': index3_reel_suivant,
                    'correct': est_correct
                })
                
                resultats['total_predictions'] += 1
                if est_correct:
                    resultats['predictions_correctes'] += 1
        
        # Calculer la précision
        if resultats['total_predictions'] > 0:
            resultats['precision'] = (resultats['predictions_correctes'] / 
                                    resultats['total_predictions']) * 100
        
        return resultats
    
    def tester_toutes_parties(self, dataset: Dict) -> Dict:
        """
        Teste les prédictions sur toutes les parties du dataset
        """
        print("🎯 Test des prédictions sur toutes les parties...")
        
        parties = dataset.get('parties_condensees', [])
        resultats_globaux = {
            'total_parties': len(parties),
            'total_predictions': 0,
            'predictions_correctes': 0,
            'precision_globale': 0.0,
            'resultats_par_partie': []
        }
        
        for partie in parties:
            resultats_partie = self.tester_predictions_partie(partie)
            resultats_globaux['resultats_par_partie'].append(resultats_partie)
            
            resultats_globaux['total_predictions'] += resultats_partie['total_predictions']
            resultats_globaux['predictions_correctes'] += resultats_partie['predictions_correctes']
            
            # Affichage du progrès
            if resultats_partie['total_predictions'] > 0:
                print(f"  Partie {resultats_partie['partie_number']:3d}: "
                      f"{resultats_partie['predictions_correctes']:2d}/{resultats_partie['total_predictions']:2d} "
                      f"({resultats_partie['precision']:5.1f}%)")
        
        # Calculer la précision globale
        if resultats_globaux['total_predictions'] > 0:
            resultats_globaux['precision_globale'] = (
                resultats_globaux['predictions_correctes'] / 
                resultats_globaux['total_predictions']
            ) * 100
        
        return resultats_globaux
    
    def afficher_resultats(self, resultats: Dict):
        """
        Affiche un résumé des résultats
        """
        print("\n" + "="*80)
        print("📊 RÉSULTATS DU PRÉDICTEUR ULTRA SIMPLE")
        print("="*80)
        print(f"Total parties testées    : {resultats['total_parties']}")
        print(f"Total prédictions        : {resultats['total_predictions']}")
        print(f"Prédictions correctes    : {resultats['predictions_correctes']}")
        print(f"PRÉCISION GLOBALE        : {resultats['precision_globale']:.2f}%")
        print("="*80)
        
        # Statistiques par partie
        precisions = [r['precision'] for r in resultats['resultats_par_partie'] 
                     if r['total_predictions'] > 0]
        
        if precisions:
            print(f"Précision moyenne        : {sum(precisions)/len(precisions):.2f}%")
            print(f"Précision minimale       : {min(precisions):.2f}%")
            print(f"Précision maximale       : {max(precisions):.2f}%")
        
        print("="*80)


def main():
    """
    Fonction principale
    """
    print("🎯 PRÉDICTEUR ULTRA SIMPLE BACCARAT")
    print("="*50)
    
    try:
        # Initialiser le prédicteur
        predicteur = PredicteurUltraSimple()
        
        # Charger le dataset
        dataset = predicteur.charger_dataset('dataset_baccarat_lupasco_20250704_180443_condensed.json')
        
        # Tester les prédictions
        resultats = predicteur.tester_toutes_parties(dataset)
        
        # Afficher les résultats
        predicteur.afficher_resultats(resultats)
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
