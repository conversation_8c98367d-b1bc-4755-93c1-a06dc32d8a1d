#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 PRÉDICTEUR ULTRA SIMPLE BACCARAT
Basé sur le tableau de transitions INDEX5 du rapport
"""

import json
import re
from typing import Dict, List, Optional

class PredicteurUltraSimple:
    """
    Prédicteur ultra simple basé sur le tableau de transitions INDEX5
    """
    
    def __init__(self):
        self.table_transitions_index5 = {}  # Première table (18 INDEX5)
        self.table_transitions_index1_index2 = {}  # Seconde table (6 INDEX1_INDEX2)

        # Système de switching adaptatif
        self.table_active = "INDEX5"  # Commence par la table INDEX5 (SWITCHING DÉSACTIVÉ)

        # Système d'anti-prédiction
        self.anti_prediction_active = True  # ACTIVÉ

        # Compteurs de debug pour anti-prédiction
        self.debug_inversions_banker_to_player = 0
        self.debug_inversions_player_to_banker = 0
        self.debug_tie_unchanged = 0

        # Debug pour vérifier la lecture du tableau
        self.debug_predictions_samples = []

        # Charger les deux tables
        self.charger_table_transitions_index5()
        self.charger_table_transitions_index1_index2()

    def appliquer_anti_prediction(self, index3_original: str) -> str:
        """
        Applique l'anti-prédiction : inverse BANKER ↔ PLAYER, garde TIE

        Args:
            index3_original: La valeur INDEX3 originale (BANKER, PLAYER, TIE)

        Returns:
            La valeur INDEX3 inversée ou identique pour TIE
        """
        if not self.anti_prediction_active:
            return index3_original

        if index3_original == "BANKER":
            self.debug_inversions_banker_to_player += 1
            return "PLAYER"
        elif index3_original == "PLAYER":
            self.debug_inversions_player_to_banker += 1
            return "BANKER"
        else:  # TIE
            self.debug_tie_unchanged += 1
            return "TIE"
    
    def charger_table_transitions_index5(self):
        """
        Charge la première table de transitions INDEX5 (18 entrées)
        """
        print("📊 Chargement de la table INDEX5...")

        try:
            with open('rapport_transitions_index5_condense_20250705_201058.txt', 'r', encoding='utf-8') as f:
                contenu = f.read()

            # Extraire le premier tableau (lignes 9-26)
            lignes = contenu.split('\n')

            # Trouver les lignes de données directement
            # Les données commencent après la ligne avec INDEX5_SOURCE
            debut_donnees = None

            for i, ligne in enumerate(lignes):
                if 'INDEX5_SOURCE' in ligne and '|' in ligne:
                    debut_donnees = i + 2  # Après la ligne de séparation
                    break

            if debut_donnees is None:
                raise ValueError("Impossible de trouver l'en-tête INDEX5_SOURCE")

            # Parser les lignes de données (18 INDEX5 attendus)
            index5_attendus = [
                '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', '1_A_BANKER', '1_B_BANKER', '1_C_BANKER',
                '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER', '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER',
                '0_A_TIE', '0_B_TIE', '0_C_TIE', '1_A_TIE', '1_B_TIE', '1_C_TIE'
            ]

            for i in range(debut_donnees, min(debut_donnees + 20, len(lignes))):
                ligne = lignes[i].strip()
                if ligne and not ligne.startswith('-') and '|' in ligne:
                    # Vérifier si la ligne contient un INDEX5 attendu
                    for index5 in index5_attendus:
                        if ligne.startswith(index5):
                            self.parser_ligne_transition_index5(ligne)
                            break

            print(f"✅ Table INDEX5 chargée : {len(self.table_transitions_index5)} entrées")

        except Exception as e:
            print(f"❌ Erreur lors du chargement INDEX5 : {e}")
            raise

    def charger_table_transitions_index1_index2(self):
        """
        Charge la seconde table de transitions INDEX1_INDEX2 (ACTIVE)
        """
        print("📊 Chargement de la table INDEX1_INDEX2...")

        try:
            with open('rapport_transitions_index5_condense_20250705_201058.txt', 'r', encoding='utf-8') as f:
                contenu = f.read()

            # Extraire le second tableau (INDEX1_INDEX2)
            lignes = contenu.split('\n')

            # Trouver les lignes de données du second tableau
            # Chercher "INDEX1_INDEX2" dans l'en-tête
            debut_donnees = None

            for i, ligne in enumerate(lignes):
                if 'INDEX1_INDEX2' in ligne and '|' in ligne:
                    debut_donnees = i + 2  # Après la ligne de séparation
                    break

            if debut_donnees is None:
                raise ValueError("Impossible de trouver l'en-tête INDEX1_INDEX2")

            # Parser les lignes de données (6 INDEX1_INDEX2 attendus)
            index1_index2_attendus = ['0_A', '0_B', '0_C', '1_A', '1_B', '1_C']

            for i in range(debut_donnees, min(debut_donnees + 10, len(lignes))):
                ligne = lignes[i].strip()
                if ligne and not ligne.startswith('-') and '|' in ligne:
                    # Vérifier si la ligne contient un INDEX1_INDEX2 attendu
                    for index1_index2 in index1_index2_attendus:
                        if ligne.startswith(index1_index2):
                            self.parser_ligne_transition_index1_index2(ligne)
                            break

            print(f"✅ Table INDEX1_INDEX2 chargée : {len(self.table_transitions_index1_index2)} entrées")

            # Debug: afficher les INDEX1_INDEX2 chargés
            if len(self.table_transitions_index1_index2) == 0:
                print("🔍 Debug: Aucun INDEX1_INDEX2 trouvé. Affichage des lignes du second tableau:")
                for i in range(35, min(50, len(lignes))):
                    if '|' in lignes[i]:
                        print(f"  Ligne {i}: {lignes[i][:100]}...")

        except Exception as e:
            print(f"❌ Erreur lors du chargement INDEX1_INDEX2 : {e}")
            raise
    
    def parser_ligne_transition_index5(self, ligne: str):
        """
        Parse une ligne du premier tableau INDEX5
        Format: INDEX5_SOURCE | Main_02 | Main_03 | ... | Main_60
        """
        # Diviser par le séparateur |
        parties = [p.strip() for p in ligne.split('|')]

        if len(parties) < 60:  # INDEX5_SOURCE + 59 colonnes (Main_02 à Main_60)
            return

        index5_source = parties[0]

        # Créer le dictionnaire des prédictions pour cet INDEX5
        predictions = {}
        for i, prediction in enumerate(parties[1:], start=2):  # Commence à Main_02
            if prediction and prediction != '-':
                predictions[f"Main_{i:02d}"] = prediction

        self.table_transitions_index5[index5_source] = predictions

    def parser_ligne_transition_index1_index2(self, ligne: str):
        """
        Parse une ligne du second tableau INDEX1_INDEX2
        Format: INDEX1_INDEX2 | Main_02 | Main_03 | ... | Main_60
        """
        # Diviser par le séparateur |
        parties = [p.strip() for p in ligne.split('|')]

        if len(parties) < 60:  # INDEX1_INDEX2 + 59 colonnes (Main_02 à Main_60)
            return

        index1_index2_source = parties[0]

        # Créer le dictionnaire des prédictions pour cet INDEX1_INDEX2
        predictions = {}
        for i, prediction in enumerate(parties[1:], start=2):  # Commence à Main_02
            if prediction and prediction != '-':
                predictions[f"Main_{i:02d}"] = prediction

        self.table_transitions_index1_index2[index1_index2_source] = predictions
    
    def charger_dataset(self, fichier_dataset: str) -> Dict:
        """
        Charge le dataset baccarat
        """
        print(f"📂 Chargement du dataset : {fichier_dataset}")
        
        try:
            with open(fichier_dataset, 'r', encoding='utf-8') as f:
                dataset = json.load(f)
            
            nb_parties = len(dataset.get('parties_condensees', []))
            print(f"✅ Dataset chargé : {nb_parties} parties")
            return dataset
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement du dataset : {e}")
            raise
    
    def extraire_index3_de_index5(self, index5: str) -> Optional[str]:
        """
        Extrait INDEX3 (BANKER/PLAYER/TIE) de INDEX5
        Format INDEX5: INDEX1_INDEX2_INDEX3
        """
        if not index5 or '_' not in index5:
            return None
        
        parties = index5.split('_')
        if len(parties) >= 3:
            return parties[2]  # INDEX3
        
        return None
    
    def extraire_index1_index2_de_index5(self, index5: str) -> Optional[str]:
        """
        Extrait INDEX1_INDEX2 de INDEX5
        Format INDEX5: INDEX1_INDEX2_INDEX3 → INDEX1_INDEX2
        """
        if not index5 or '_' not in index5:
            return None

        parties = index5.split('_')
        if len(parties) >= 2:
            return f"{parties[0]}_{parties[1]}"  # INDEX1_INDEX2

        return None

    def effectuer_switch(self):
        """
        Effectue le switch entre les deux tables
        """
        if self.table_active == "INDEX1_INDEX2":
            self.table_active = "INDEX5"
            print(f"    🔄 SWITCH vers table INDEX5 (après 1 échec)")
        else:
            self.table_active = "INDEX1_INDEX2"
            print(f"    🔄 SWITCH vers table INDEX1_INDEX2 (après 1 échec)")

    def predire_main_suivante(self, index5_actuel: str, position_actuelle: int) -> Optional[str]:
        """
        Prédit INDEX3 pour la main suivante avec système de switching adaptatif

        Args:
            index5_actuel: INDEX5 observé à la position actuelle
            position_actuelle: Position actuelle (1-59)

        Returns:
            INDEX3 prédit pour la position suivante (BANKER/PLAYER/TIE)
        """
        position_suivante = position_actuelle + 1
        if position_suivante > 60:
            return None

        colonne_suivante = f"Main_{position_suivante:02d}"

        # Prédiction selon la table active
        if self.table_active == "INDEX1_INDEX2":
            # Utiliser la table INDEX1_INDEX2
            index1_index2_actuel = self.extraire_index1_index2_de_index5(index5_actuel)

            if not index1_index2_actuel or index1_index2_actuel not in self.table_transitions_index1_index2:
                return None

            index5_predit = self.table_transitions_index1_index2[index1_index2_actuel].get(colonne_suivante)
        else:
            # Utiliser la table INDEX5
            if not index5_actuel or index5_actuel not in self.table_transitions_index5:
                return None

            index5_predit = self.table_transitions_index5[index5_actuel].get(colonne_suivante)

        if not index5_predit:
            return None

        # Extraire INDEX3 de l'INDEX5 prédit
        index3_original = self.extraire_index3_de_index5(index5_predit)

        # Debug: Enregistrer quelques échantillons pour vérification
        if len(self.debug_predictions_samples) < 10:
            self.debug_predictions_samples.append({
                'index5_actuel': index5_actuel,
                'position_actuelle': position_actuelle,
                'position_suivante': position_suivante,
                'colonne_suivante': colonne_suivante,
                'table_active': self.table_active,
                'index5_predit': index5_predit,
                'index3_original': index3_original
            })

        # Appliquer l'anti-prédiction (inversion BANKER ↔ PLAYER)
        index3_final = self.appliquer_anti_prediction(index3_original)

        return index3_final

    def mettre_a_jour_switch(self, prediction_correcte: bool):
        """
        Met à jour le système de switching basé sur le résultat de la prédiction
        DÉSACTIVÉ - Ne fait plus de switching

        Args:
            prediction_correcte: True si la prédiction était correcte, False sinon
        """
        # SWITCHING DÉSACTIVÉ - Ne fait rien
        pass
    
    def tester_predictions_partie(self, partie: Dict) -> Dict:
        """
        Teste les prédictions sur une partie complète avec système de switching

        Returns:
            Dictionnaire avec les résultats des prédictions
        """
        mains = partie.get('mains_condensees', [])
        partie_number = partie.get('partie_number', 0)

        resultats = {
            'partie_number': partie_number,
            'predictions': [],
            'total_predictions': 0,
            'predictions_correctes': 0,
            'precision': 0.0,
            'switches': 0,  # Nombre de switches dans cette partie
            'table_finale': self.table_active  # Table active à la fin de la partie
        }

        table_precedente = self.table_active  # Pour détecter les switches

        # Parcourir les mains de 1 à 59 pour faire des prédictions
        for i in range(len(mains) - 1):
            main_actuelle = mains[i]
            main_suivante = mains[i + 1]

            # Vérifier que les mains sont valides
            if (main_actuelle.get('main_number') is None or
                main_suivante.get('main_number') is None):
                continue

            position_actuelle = main_actuelle.get('main_number')
            index5_actuel = main_actuelle.get('index5')
            index3_reel_suivant = main_suivante.get('index3')

            if not index5_actuel or not index3_reel_suivant:
                continue

            # Faire la prédiction
            table_utilisee = self.table_active  # Sauvegarder quelle table est utilisée
            index3_predit = self.predire_main_suivante(index5_actuel, position_actuelle)

            if index3_predit is not None:
                est_correct = (index3_predit == index3_reel_suivant)

                resultats['predictions'].append({
                    'position_actuelle': position_actuelle,
                    'index5_actuel': index5_actuel,
                    'index3_predit': index3_predit,
                    'index3_reel': index3_reel_suivant,
                    'correct': est_correct,
                    'table_utilisee': table_utilisee
                })

                resultats['total_predictions'] += 1
                if est_correct:
                    resultats['predictions_correctes'] += 1

                # Mettre à jour le système de switching
                self.mettre_a_jour_switch(est_correct)

                # Compter les switches dans cette partie
                if self.table_active != table_precedente:
                    resultats['switches'] += 1
                    table_precedente = self.table_active

        # Calculer la précision
        if resultats['total_predictions'] > 0:
            resultats['precision'] = (resultats['predictions_correctes'] /
                                    resultats['total_predictions']) * 100

        resultats['table_finale'] = self.table_active
        return resultats
    
    def tester_toutes_parties(self, dataset: Dict) -> Dict:
        """
        Teste les prédictions sur toutes les parties du dataset avec switching adaptatif
        """
        print("🎯 Test des prédictions avec table INDEX5 fixe SEULE...")
        print(f"    Table utilisée : {self.table_active} (SWITCHING DÉSACTIVÉ)")
        print(f"    Anti-prédiction : {'ACTIVÉE' if self.anti_prediction_active else 'DÉSACTIVÉE'}")

        parties = dataset.get('parties_condensees', [])
        resultats_globaux = {
            'total_parties': len(parties),
            'total_predictions': 0,
            'predictions_correctes': 0,
            'precision_globale': 0.0,
            'total_switches': 0,
            'resultats_par_partie': []
        }

        for partie in parties:
            resultats_partie = self.tester_predictions_partie(partie)
            resultats_globaux['resultats_par_partie'].append(resultats_partie)

            resultats_globaux['total_predictions'] += resultats_partie['total_predictions']
            resultats_globaux['predictions_correctes'] += resultats_partie['predictions_correctes']
            resultats_globaux['total_switches'] += resultats_partie['switches']

            # Affichage du progrès avec info sur les switches
            if resultats_partie['total_predictions'] > 0:
                switch_info = f" [S:{resultats_partie['switches']}]" if resultats_partie['switches'] > 0 else ""
                table_info = f" ({resultats_partie['table_finale'][:3]})"
                print(f"  Partie {resultats_partie['partie_number']:3d}: "
                      f"{resultats_partie['predictions_correctes']:2d}/{resultats_partie['total_predictions']:2d} "
                      f"({resultats_partie['precision']:5.1f}%){switch_info}{table_info}")

        # Calculer la précision globale
        if resultats_globaux['total_predictions'] > 0:
            resultats_globaux['precision_globale'] = (
                resultats_globaux['predictions_correctes'] /
                resultats_globaux['total_predictions']
            ) * 100

        return resultats_globaux
    
    def afficher_resultats(self, resultats: Dict):
        """
        Affiche un résumé des résultats avec informations sur le switching
        """
        print("\n" + "="*80)
        print("📊 RÉSULTATS DU PRÉDICTEUR AVEC SWITCHING ADAPTATIF")
        print("="*80)
        print(f"Total parties testées    : {resultats['total_parties']}")
        print(f"Total prédictions        : {resultats['total_predictions']}")
        print(f"Prédictions correctes    : {resultats['predictions_correctes']}")
        print(f"PRÉCISION GLOBALE        : {resultats['precision_globale']:.2f}%")
        print(f"Total switches effectués : {resultats['total_switches']}")
        print(f"Table finale active      : {self.table_active}")
        print("="*80)

        # Statistiques par partie
        precisions = [r['precision'] for r in resultats['resultats_par_partie']
                     if r['total_predictions'] > 0]

        if precisions:
            print(f"Précision moyenne        : {sum(precisions)/len(precisions):.2f}%")
            print(f"Précision minimale       : {min(precisions):.2f}%")
            print(f"Précision maximale       : {max(precisions):.2f}%")

        # Statistiques sur les switches
        parties_avec_switches = [r for r in resultats['resultats_par_partie'] if r['switches'] > 0]
        if parties_avec_switches:
            print(f"Parties avec switches    : {len(parties_avec_switches)}")
            switches_par_partie = [r['switches'] for r in parties_avec_switches]
            print(f"Switches max par partie  : {max(switches_par_partie)}")
            print(f"Switches moy par partie  : {sum(switches_par_partie)/len(switches_par_partie):.1f}")

        # Répartition des tables finales
        tables_finales = [r['table_finale'] for r in resultats['resultats_par_partie']]
        count_index5 = tables_finales.count('INDEX5')
        count_index1_index2 = tables_finales.count('INDEX1_INDEX2')
        print(f"Parties finies sur INDEX5     : {count_index5}")
        print(f"Parties finies sur INDEX1_INDEX2 : {count_index1_index2}")

        # Statistiques de debug anti-prédiction
        if self.anti_prediction_active:
            print("="*80)
            print("🔍 DEBUG ANTI-PRÉDICTION")
            print("="*80)
            total_inversions = self.debug_inversions_banker_to_player + self.debug_inversions_player_to_banker
            print(f"BANKER → PLAYER inversions : {self.debug_inversions_banker_to_player}")
            print(f"PLAYER → BANKER inversions : {self.debug_inversions_player_to_banker}")
            print(f"TIE inchangés             : {self.debug_tie_unchanged}")
            print(f"Total inversions          : {total_inversions}")
            print(f"Total prédictions         : {total_inversions + self.debug_tie_unchanged}")

        # Debug échantillons de prédictions
        if self.debug_predictions_samples:
            print("="*80)
            print("🔍 DEBUG ÉCHANTILLONS DE PRÉDICTIONS")
            print("="*80)
            for i, sample in enumerate(self.debug_predictions_samples):
                print(f"Échantillon {i+1}:")
                print(f"  INDEX5 actuel     : {sample['index5_actuel']}")
                print(f"  Position actuelle : {sample['position_actuelle']}")
                print(f"  Position suivante : {sample['position_suivante']}")
                print(f"  Colonne suivante  : {sample['colonne_suivante']}")
                print(f"  Table active      : {sample['table_active']}")
                print(f"  INDEX5 prédit     : {sample['index5_predit']}")
                print(f"  INDEX3 original   : {sample['index3_original']}")
                print()

        print("="*80)


def main():
    """
    Fonction principale
    """
    print("🎯 PRÉDICTEUR ULTRA SIMPLE INDEX5 SEUL (ANTI-PRÉDICTION + SWITCHING DÉSACTIVÉS)")
    print("="*70)
    
    try:
        # Initialiser le prédicteur
        predicteur = PredicteurUltraSimple()
        
        # Charger le dataset
        dataset = predicteur.charger_dataset('dataset_baccarat_lupasco_20250704_180443_condensed.json')
        
        # Tester les prédictions
        resultats = predicteur.tester_toutes_parties(dataset)
        
        # Afficher les résultats
        predicteur.afficher_resultats(resultats)
        
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
